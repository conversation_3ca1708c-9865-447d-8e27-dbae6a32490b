import React, { useState, useContext, useEffect, useCallback } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import {
  Box,
  Collapse,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Paper,
  Button,
  withStyles,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Snackbar,
  SnackbarContent,
} from '@material-ui/core';
import KeyboardArrowDownIcon from '@material-ui/icons/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@material-ui/icons/KeyboardArrowUp';
import EditIcon from '@material-ui/icons/Edit';
import DeleteIcon from '@material-ui/icons/Delete';
import PlayArrowIcon from '@material-ui/icons/PlayArrow';
import StopIcon from '@material-ui/icons/Stop';
import CheckCircleIcon from '@material-ui/icons/CheckCircle';
import { UserContext } from '../../../context/UserProvider';
import moment from 'moment';
import 'moment/locale/es';
import { db } from '../../../config/firebase';
import { get_Uid_Sate_ForSwitch } from '../../../context/functions/DashboardFunctions/divideUIDByKind';
import { IN_SWITCH } from '../../../constants/globalConst';
import { keys } from 'highcharts';

const StyledTableCell = withStyles((theme) => ({
  head: {
    backgroundColor: "#3B3C43",
    color: " #FFFFFF",
  },
  body: {
    fontSize: 14,
  },
}))(TableCell);

// Estilos para la tabla
const useStyles = makeStyles((theme) => ({
  root: {
    width: '100%',
    marginTop: theme.spacing(3),
  },
  table: {
    minWidth: 650,
  },
  tableContainer: {
    marginBottom: theme.spacing(2),
  },
  actionButton: {
    marginRight: theme.spacing(1),
  },
  detailsTable: {
    marginBottom: theme.spacing(2),
  },
}));

// Función para obtener el estado actual de un switch específico
const getSwitchState = async (switchUid, usuario) => {
  if (!switchUid || !usuario || !usuario.username) {
    console.log("No hay switch configurado o usuario no disponible");
    return null;
  }

  try {
    // Extraer el nodeId, canId y outId del UID del switch (formato: macId@canId@kind@outId)
    const switchParts = switchUid.split('@');
    if (switchParts.length < 4) {
      console.error("Formato de UID de switch inválido:", switchUid);
      return null;
    }

    const macId = switchParts[0];
    const canId = switchParts[1];
    const outId = switchParts[3];

    // Obtener los datos del switch desde Firebase
    const docPath = `${usuario.username}/infoDevices/${macId}/${canId}/fromModule/render`;
    const docRef = await db.doc(docPath).get();

    if (!docRef.exists) {
      console.error("No se encontraron datos para el switch:", switchUid);
      return null;
    }

    const switchData = docRef.data().C_bool;
    const resp = switchData[Number(outId)];

    return resp;
  } catch (error) {
    console.error("Error al obtener el estado del switch:", error);
    return null;
  }
};

// Componente para cada fila de la tabla
function Row(props) {
  const { row, handleExecute, handleEdit, handleDelete, isExecuting, onShowAlert, isAnyTankExecuting } = props;
  const [open, setOpen] = useState(false);
  const [actionSelected, setActionSelected] = useState(row.action === "fill" ? 1 : (row.action === "empty" ? 0 : 1))
  const [maxSwitchState, setMaxSwitchState] = useState(null);
  const [minSwitchState, setMinSwitchState] = useState(null);
  const [buttonDisabled, setButtonDisabled] = useState(false);
  const [tooltipText, setTooltipText] = useState("");
  const [initialSwitchState, setInitialSwitchState] = useState(null);
  const [operationCompleted, setOperationCompleted] = useState(false);

  const classes = useStyles();
  const { userTimezone, usuario } = useContext(UserContext);

  // Función para actualizar el estado del botón según la acción y el estado de los switches
  const updateButtonState = useCallback((action, maxState, minState) => {
    if (isExecuting) {
      // Si está ejecutando, el botón siempre está habilitado para poder detener
      setButtonDisabled(false);
      setTooltipText("");
      return;
    }

    // Si hay otro tanque ejecutándose, deshabilitar este botón
    if (isAnyTankExecuting && !isExecuting) {
      setButtonDisabled(true);
      setTooltipText("No se puede ejecutar porque hay otro tanque en operación");
      return;
    }

    if (action === "fill" && maxState === "1") {
      setButtonDisabled(true);
      setTooltipText("No se puede llenar porque el sensor de nivel máximo está activado");
    } else if (action === "empty" && minState === "0") {
      setButtonDisabled(true);
      setTooltipText("No se puede vaciar porque el sensor de nivel mínimo está desactivado");
    } else {
      setButtonDisabled(false);
      setTooltipText("");
    }
  }, [isExecuting, isAnyTankExecuting, setButtonDisabled, setTooltipText]);

  // Función para mostrar alerta cuando se completa la operación
  const showCompletionAlert = useCallback((action) => {
    const actionText = action === "fill" ? "llenado" : "vaciado";
    const message = `Operación de ${actionText} completada para ${row.name}`;

    // Usar la función de alerta del componente padre
    if (onShowAlert) {
      onShowAlert(message);
    }
  }, [row.name, onShowAlert]);

  // Efecto para monitorear el estado de los switches
  useEffect(() => {
    const checkSwitchesState = async () => {
      // Obtener los UIDs de los switches de nivel máximo y mínimo
      const maxLevelSwitchUid = row.fillData?.maxSwitch;
      const minLevelSwitchUid = row.emptyData?.minSwitch;

      let maxState = null;
      let minState = null;

      // Verificar el estado de los switches
      if (maxLevelSwitchUid) {
        maxState = await getSwitchState(maxLevelSwitchUid, usuario);
        setMaxSwitchState(maxState);
      }

      if (minLevelSwitchUid) {
        minState = await getSwitchState(minLevelSwitchUid, usuario);
        setMinSwitchState(minState);
      }

      // Actualizar el estado del botón según la acción seleccionada y el estado de los switches
      updateButtonState(actionSelected === 1 ? "fill" : "empty", maxState, minState);

      // Monitorear cambios en el estado del switch para detectar finalización de operación
      if (isExecuting && !operationCompleted) {
        const currentAction = actionSelected === 1 ? "fill" : "empty";
        const relevantSwitchState = currentAction === "fill" ? maxState : minState;

        // Si es la primera vez que se ejecuta después de iniciar la operación, guardar el estado inicial
        if (initialSwitchState === null) {
          setInitialSwitchState(relevantSwitchState);
        }
        // Si ya tenemos un estado inicial y el estado actual ha cambiado, la operación se ha completado
        else if (initialSwitchState !== relevantSwitchState) {
          // Verificar que el cambio sea el esperado según la operación
          const expectedChange = currentAction === "fill" ? "1" : "0";
          if (relevantSwitchState === expectedChange) {
            setOperationCompleted(true);
            showCompletionAlert(currentAction);

            // Notificar al componente padre que la operación ha terminado por un cambio en el Switch
            handleExecute(row, true, true);
          }
        }
      }
    };

    // Verificar el estado inicial
    checkSwitchesState();

    // Configurar un intervalo para verificar periódicamente el estado de los switches
    const intervalId = setInterval(checkSwitchesState, 5000); // Verificar cada 5 segundos

    // Limpiar el intervalo cuando el componente se desmonte
    return () => clearInterval(intervalId);
  }, [row, actionSelected, usuario, updateButtonState, isExecuting, initialSwitchState, operationCompleted, showCompletionAlert, handleExecute]);

  // Resetear el estado de monitoreo cuando cambia el estado de ejecución
  useEffect(() => {
    if (!isExecuting) {
      setInitialSwitchState(null);
      setOperationCompleted(false);
    }
  }, [isExecuting]);

  const handleActionChange = (event, tankId) => {
    const newAction = event.target.value === 1 ? "fill" : "empty";
    handleEdit({ ...row, action: newAction });
    setActionSelected(event.target.value);

    // Actualizar el estado del botón cuando cambia la acción
    updateButtonState(newAction, maxSwitchState, minSwitchState);
  };

  return (
    <React.Fragment>
      {/* Fila principal */}
      <TableRow>
        <TableCell>
          <IconButton
            aria-label="expand row"
            size="small"
            onClick={() => setOpen(!open)}
          >
            {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
          </IconButton>
        </TableCell>
        <TableCell component="th" scope="row">
          {row.name}
        </TableCell>
        <TableCell>
          <FormControl className={classes.formControl}>
            <Select
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              value={actionSelected}
              onChange={(e) => handleActionChange(e, row.id)}
            >
              <MenuItem value={1}>Llenar</MenuItem>
              <MenuItem value={0}>Vaciar</MenuItem>
            </Select>
          </FormControl>
        </TableCell>
        <TableCell>
          {row.lastExecution !== "Never" ? moment(row.lastExecution).tz(userTimezone || 'America/Mexico_City').format('DD/MM/YYYY, HH:mm [Hrs.]') : 'Nunca'}
        </TableCell>
        <TableCell>
          <Tooltip title={tooltipText} placement="top" arrow>
            <span>
              <Button
                variant="contained"
                color={isExecuting ? "secondary" : "primary"}
                size="small"
                startIcon={isExecuting ? <StopIcon /> : <PlayArrowIcon />}
                onClick={() => handleExecute(row, false, false)}
                data-tank-id={row.id}
                disabled={buttonDisabled && !isExecuting}
              >
                {isExecuting ? 'Detener' : 'Ejecutar'}
              </Button>
            </span>
          </Tooltip>
        </TableCell>
        <TableCell>
          <IconButton
            aria-label="editar"
            className={classes.actionButton}
            onClick={() => {
              // Pasamos el objeto row sin la propiedad action para diferenciar
              // entre cambio de acción y edición completa
              const { action, ...tankWithoutAction } = row;
              handleEdit(tankWithoutAction);
            }}
          >
            <EditIcon />
          </IconButton>
          <IconButton
            aria-label="eliminar"
            onClick={() => handleDelete(row)}
          >
            <DeleteIcon />
          </IconButton>
        </TableCell>
      </TableRow>

      {/* Fila de detalles (expandible) */}
      <TableRow key={`${row.id}-details-row`}>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={6}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Box margin={1}>
              <Typography variant="h6" gutterBottom component="div">
                Detalles de Configuración
              </Typography>
              <Table size="small" className={classes.detailsTable}>
                {/* <TableHead>
                  <TableRow>
                    <TableCell>Parámetro</TableCell>
                    <TableCell>Valor</TableCell>
                  </TableRow>
                </TableHead> */}
                <TableBody>
                  {row.details && Object.entries(row.details).map(([key, value], index) => (
                    <TableRow key={`${row.id}-detail-${index}`}>
                      <TableCell component="th" scope="row">
                        {key}
                      </TableCell>
                      <TableCell>{value}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
    </React.Fragment>
  );
}

// Componente principal de la tabla
export default function WaterTankCollapsibleTable({ tanks, onExecute, onEdit, onDelete }) {
  const classes = useStyles();
  const [executingTankId, setExecutingTankId] = useState(null);
  // Estado para manejar las alertas a nivel de la tabla
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");

  const handleExecute = (tank, completed = false, stoppedBySwitch = false) => {
    if (onExecute) {
      // Llamar a la función de ejecución del componente padre
      onExecute(tank, completed, stoppedBySwitch);

      // Si la operación se completó o se detuvo, resetear el estado de ejecución
      if (completed || stoppedBySwitch) {
        setExecutingTankId(null);
      } else {
        // Si el tanque ya está ejecutándose, lo detenemos
        if (executingTankId === tank.id) {
          setExecutingTankId(null);
        } else {
          // Si no está ejecutándose y no hay otro tanque ejecutándose, lo iniciamos
          if (executingTankId === null) {
            setExecutingTankId(tank.id);
          }
        }
      }
    };
  };

  const handleEdit = (tank) => {
    if (onEdit) onEdit(tank);
  };

  const handleDelete = (tank) => {
    if (onDelete) onDelete(tank);
  };

  // Función para mostrar alertas desde los componentes Row
  const showCompletionAlert = (message) => {
    setAlertMessage(message);
    setShowAlert(true);

    // Cerrar la alerta después de 5 segundos
    setTimeout(() => {
      setShowAlert(false);
    }, 5000);
  };

  // Estilo para la alerta de éxito
  const successAlertStyle = {
    backgroundColor: '#4caf50',
    color: 'white',
    display: 'flex',
    alignItems: 'center',
  };

  return (
    <React.Fragment>
      <TableContainer component={Paper} className={classes.tableContainer}>
        <Table className={classes.table} aria-label="collapsible table">
          <TableHead>
            <TableRow>
              <StyledTableCell />
              <StyledTableCell align="left">Tanques</StyledTableCell>
              <StyledTableCell align="left">Acción</StyledTableCell>
              <StyledTableCell align="left">Última ejecución</StyledTableCell>
              <StyledTableCell align="left">Ejecutar</StyledTableCell>
              <StyledTableCell align="left">Editar/Eliminar</StyledTableCell>
              {/* <TableCell>Acción</TableCell>
              <TableCell>Última ejecución</TableCell>
              <TableCell>Ejecutar</TableCell>
              <TableCell>Editar/Eliminar</TableCell> */}
            </TableRow>
          </TableHead>
          <TableBody>
            {tanks && tanks.map((tank) => (
              <Row
                key={tank.id}
                row={tank}
                handleExecute={handleExecute}
                handleEdit={handleEdit}
                handleDelete={handleDelete}
                isExecuting={executingTankId === tank.id}
                isAnyTankExecuting={executingTankId !== null}
                onShowAlert={showCompletionAlert}
              />
            ))}
            {(!tanks || tanks.length === 0) && (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  No hay tanques configurados
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Alerta de operación completada - ahora fuera del TableBody */}
      <Snackbar
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        open={showAlert}
        autoHideDuration={5000}
        onClose={() => setShowAlert(false)}
      >
        <SnackbarContent
          style={successAlertStyle}
          message={
            <span style={{ display: 'flex', alignItems: 'center' }}>
              <CheckCircleIcon style={{ marginRight: '8px' }} />
              {alertMessage}
            </span>
          }
        />
      </Snackbar>
    </React.Fragment>
  );
}
